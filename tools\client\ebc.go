package client

import (
	"context"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/config"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources"
	opssso_api "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/bpm/oauth"
)

var (
	EbcSso *opssso_api.OAuth
)

func init() {
	EbcSso = NewEbcSso(context.Background())
}

func NewEbcSso(ctx context.Context) *opssso_api.OAuth {
	cfg := config.NewConfig(ctx, "")
	cfg.CurrenBaseOn = config.BaseOnK8s
	cicdAppProject := resources.NewResourceController[*opssso_api.OAuth](cfg)
	return cicdAppProject
}
