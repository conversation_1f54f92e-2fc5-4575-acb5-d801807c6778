package controller

import (
	"context"
	errors2 "errors"
	apiv1 "intelliforge-backend/api/v1"
	"intelliforge-backend/internal/consts"
	errors3 "intelliforge-backend/internal/consts/errors"
	"intelliforge-backend/internal/model/dto"
	"intelliforge-backend/internal/model/entity"
	"intelliforge-backend/internal/service"
	"intelliforge-backend/tools/client"
	"intelliforge-backend/tools/errors"
	ttgitlab "intelliforge-backend/tools/pkg/gitlab"
	"net/http"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/util/grand"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/bpm/oauth"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/constack/tools"
)

var (
	Sso = cSso{}
)

type cSso struct{}

func (this *cSso) Login(ctx context.Context, req *apiv1.SsoLoginReq) (res *apiv1.DataRes, err error) {
	res = &apiv1.DataRes{}
	r := g.RequestFromCtx(ctx)

	config := g.Cfg("sso-config").GetAdapter().(*gcfg.AdapterFile)
	config.SetFileName("sso-config.yaml")
	privateKey, err := config.Get(ctx, "data.private_key")

	originTicket := r.GetParam("ticket", req.Ticket).String()
	log.L.WithName("cSso.Login").Infof(ctx, "origin ticket:%s", originTicket)

	username, err := client.Opssso.Validate(ctx, originTicket, privateKey.(string))
	if err != nil {
		return nil, err
	}

	if username == "" {
		return nil, errors.ValidateError("sso 认证失败")
	}

	// 注册
	cmdbUserInfo, err := client.CmdbApiJsonRpc.GetUserInfoByUsername(ctx, username)
	if err != nil {
		return nil, err
	}

	userInfo, err := service.User().GetUserByUsername(ctx, username)
	if err != nil {
		if !errors2.Is(err, errors3.ErrUserNotFound) {
			return nil, err
		}
		digits := grand.Digits(8)
		encryptPass, err := tools.EncryptData(digits, consts.DefaultCipherKey)
		if err != nil {
			return nil, err
		}

		// 获取 gitlab 用户信息
		var userGitlabId int
		gitlabUserInfo, _ := ttgitlab.GitlabClient.GetUserByEmail(ctx, cmdbUserInfo.Email)
		if gitlabUserInfo != nil {
			userGitlabId = gitlabUserInfo.ID
		}

		err = service.User().CreateUser(ctx, &entity.User{
			Uid:        cmdbUserInfo.UserId,
			Username:   cmdbUserInfo.Name,
			NickName:   cmdbUserInfo.RealName,
			Email:      cmdbUserInfo.Email,
			EmployeeNo: cmdbUserInfo.UserNo,
			Password:   encryptPass,
			IsActive:   consts.STrue,
			GitlabId:   int64(userGitlabId),
		})
		if err != nil {
			return nil, err
		}

		// reset
		userInfo, err = service.User().GetUserByUsername(ctx, username)
		if err != nil {
			return nil, err
		}
	}

	if userInfo.Id == 0 {
		return nil, errors3.ErrUserNotFound
	}

	err = service.PlatFormAuth().SyncUserTeam(ctx, int(userInfo.Id))
	if err != nil {
		return nil, err
	}

	cipherPass, err := tools.DecryptCipher(userInfo.Password, consts.DefaultCipherKey)
	if err != nil {
		return nil, err
	}

	r.SetCtxVar(consts.SSO, dto.UserLoginInput{
		Username: userInfo.Username,
		Password: cipherPass,
	})

	token, expire := service.JwtAuth().Do().LoginHandler(ctx)

	r.Cookie.SetCookie(consts.IntelliforgeCookieKey, token, "*", "/", expire.Sub(time.Now()))
	r.Response.RedirectTo("/", http.StatusFound)
	return res, nil
}

func (this *cSso) EBCLogin(ctx context.Context, req *apiv1.EBCLoginReq) (res *apiv1.DataRes, err error) {
	res = &apiv1.DataRes{}
	r := g.RequestFromCtx(ctx)

	clientId, _ := g.Cfg().Get(context.Background(), "ebc.app_id")
	clientSecret, _ := g.Cfg().Get(context.Background(), "ebc.app_secret")

	originTicket := r.GetParam("code").String()
	log.L.WithName("cSso.EBCLogin").Infof(ctx, "origin code:%s", originTicket)

	rst, err := client.EbcSso.GetAccessToken(&oauth.AccessTokenReq{
		GrantType:    "authorization_code",
		ClientId:     clientId.String(),
		ClientSecret: clientSecret.String(),
		Code:         originTicket,
	})
	if err != nil {
		return nil, err
	}

	if rst.UserInfo.Account == "" {
		return nil, errors.ValidateError("sso 认证失败")
	}
	username := rst.UserInfo.Account

	// 注册
	cmdbUserInfo, err := client.CmdbApiJsonRpc.GetUserInfoByUsername(ctx, username)
	if err != nil {
		log.L.WithName("cSso.EBCLogin").Errorf(ctx, "get cmdb user info error:%s", err.Error())
		return nil, err
	}

	userInfo, err := service.User().GetUserByUsername(ctx, username)
	if err != nil {
		if !errors2.Is(err, errors3.ErrUserNotFound) {
			return nil, err
		}
		digits := grand.Digits(8)
		encryptPass, err := tools.EncryptData(digits, consts.DefaultCipherKey)
		if err != nil {
			return nil, err
		}

		// 获取 gitlab 用户信息
		var userGitlabId int
		gitlabUserInfo, _ := ttgitlab.GitlabClient.GetUserByEmail(ctx, cmdbUserInfo.Email)
		if gitlabUserInfo != nil {
			userGitlabId = gitlabUserInfo.ID
		}

		err = service.User().CreateUser(ctx, &entity.User{
			Uid:        cmdbUserInfo.UserId,
			Username:   cmdbUserInfo.Name,
			NickName:   cmdbUserInfo.RealName,
			Email:      cmdbUserInfo.Email,
			EmployeeNo: cmdbUserInfo.UserNo,
			Password:   encryptPass,
			IsActive:   consts.STrue,
			GitlabId:   int64(userGitlabId),
		})
		if err != nil {
			return nil, err
		}

		// reset
		userInfo, err = service.User().GetUserByUsername(ctx, username)
		if err != nil {
			return nil, err
		}
	}

	if userInfo.Id == 0 {
		return nil, errors3.ErrUserNotFound
	}

	err = service.PlatFormAuth().SyncUserTeam(ctx, int(userInfo.Id))
	if err != nil {
		return nil, err
	}

	cipherPass, err := tools.DecryptCipher(userInfo.Password, consts.DefaultCipherKey)
	if err != nil {
		return nil, err
	}

	r.SetCtxVar(consts.SSO, dto.UserLoginInput{
		Username: userInfo.Username,
		Password: cipherPass,
	})

	token, expire := service.JwtAuth().Do().LoginHandler(ctx)

	r.Cookie.SetCookie(consts.IntelliforgeCookieKey, token, "*", "/", expire.Sub(time.Now()))
	r.Response.RedirectTo("/", http.StatusFound)
	return res, nil
}
