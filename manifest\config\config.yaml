server:
  address:     ":8008"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  mode: develop
  dumpRouterMap: false
  accessLogEnabled: true

logger:
  level : "all"
  stdout: true

grpc:
  port: 8108

app:
  # jwtAuthTimeout: hour
  jwtAuthTimeout: 720
  secreteKey: dHQtaW50ZWxsaWZvcmdlCg==
  # echo "intelliforge" |base64
  encryptKey: dHQtaW50ZWxsaWZvcmdlCg==


database:
  logger:
    level:   "all"
    stdout:  true
    ctxKeys: ["RequestId"]
  default:
    link: "mysql:root:YUE4MTYwODA2Ny4K@tcp(*************:6033)/intelliforge?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true
    charset: utf8mb4
    maxIdel: 10
    maxOpen: 200

redis:
  # 单实例
  default:
    address: *************:6379
    #    address: 127.0.0.1:6379
    db:      1
    pass:    YUE4MTYwODA2Ny4K
    #    pass:
    idleTimeout: 5m
    maxActive: 200
    readTimeout: 0
    minIdle: 10

api:
  cicd:
    authorization: c2hlbmpp.abcd8d49882ae33a09a0b1909b597877f7dd7a20042341898dbba79a73700c09
  cmdb:
    authorization: iqNOUYbEJnBJFFe2di1n1T87ssTxO1UXRZo5iIHY8Sh119IWQwsgHcQmyo49rWQd

gitlab:
  url: "https://gitlab.ttyuyin.com"
  sshUrl: "**********************"
  superToken: "********************"
  aiReviewRobotToken: cnDHCBDhW3osJVxih_tF

aiReview:
  gitlabUser: 1312 # 啄木鸟用户id
  webhookUrl: http://aaa.com # ai review webhook 地址

mcp:
  url: "http://**************:8000"

feishu:
  app_id: "cli_a653a96b82f9100d"
  app_secret: "AiJHAbsA5GGdPujVWZbHjdVc05CPqs7P"
  domain: "https://intelliforge.ttyuyin.com"

scanDomain:
  url: "http://localhost:3334"

ebc:
  app_id: "cli_a653a96b82f9100d"
  app_secret: "AiJHAbsA5GGdPujVWZbHjdVc05CPqs7P"